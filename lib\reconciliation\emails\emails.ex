defmodule Reconciliation.Emails do
  @moduledoc """
  Email templates and functions for sending emails using Swoosh.
  """
  
  import Swoosh.Email
  alias Reconciliation.Mailer

  @from_email "<EMAIL>"  # Using Resend's default sender
  @app_name "ProBASE Reconciliation"

  @doc """
  Sends a password reset email to the user.
  """
  def password_reset_email(user, reset_url) do
    new()
    |> to(user.email)
    |> from("#{@app_name} <#{@from_email}>")
    |> subject("Reset your password - #{@app_name}")
    |> html_body(password_reset_html_body(user, reset_url))
    |> text_body(password_reset_text_body(user, reset_url))
  end

  @doc """
  Sends a confirmation email to the user.
  """
  def confirmation_email(user, confirmation_url) do
    new()
    |> to(user.email)
    |> from("#{@app_name} <#{@from_email}>")
    |> subject("Confirm your account - #{@app_name}")
    |> html_body(confirmation_html_body(user, confirmation_url))
    |> text_body(confirmation_text_body(user, confirmation_url))
  end

  @doc """
  Sends an email update confirmation email.
  """
  def email_update_email(user, old_email, update_url) do
    new()
    |> to(user.email)
    |> from("#{@app_name} <#{@from_email}>")
    |> subject("Confirm your email change - #{@app_name}")
    |> html_body(email_update_html_body(user, old_email, update_url))
    |> text_body(email_update_text_body(user, old_email, update_url))
  end

  @doc """
  Sends a welcome email to a new user with their temporary password.
  """
  def welcome_email(user, temporary_password, login_url) do
    new()
    |> to(user.email)
    |> from("#{@app_name} <#{@from_email}>")
    |> subject("Welcome to #{@app_name} - Your Account Details")
    |> html_body(welcome_html_body(user, temporary_password, login_url))
    |> text_body(welcome_text_body(user, temporary_password, login_url))
  end

  # Private functions for email templates

  defp password_reset_html_body(_user, reset_url) do
    """
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Reset your password</title>
        <style>
          @media only screen and (max-width: 620px) {
            table.body h1 {
              font-size: 28px !important;
              margin-bottom: 10px !important;
            }
            table.body p,
            table.body ul,
            table.body ol,
            table.body td,
            table.body span,
            table.body a {
              font-size: 16px !important;
            }
            table.body .wrapper,
            table.body .article {
              padding: 10px !important;
            }
            table.body .content {
              padding: 0 !important;
            }
            table.body .container {
              padding: 0 !important;
              width: 100% !important;
            }
            table.body .main {
              border-left-width: 0 !important;
              border-radius: 0 !important;
              border-right-width: 0 !important;
            }
            table.body .btn table {
              width: 100% !important;
            }
            table.body .btn a {
              width: 100% !important;
            }
            table.body .img-responsive {
              height: auto !important;
              max-width: 100% !important;
              width: auto !important;
            }
          }
        </style>
      </head>
      <body style="background-color: #f6f6f6; font-family: sans-serif; -webkit-font-smoothing: antialiased; font-size: 14px; line-height: 1.4; margin: 0; padding: 0; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f6f6f6; width: 100%;" width="100%" bgcolor="#f6f6f6">
          <tr>
            <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">&nbsp;</td>
            <td class="container" style="font-family: sans-serif; font-size: 14px; vertical-align: top; display: block; max-width: 580px; padding: 10px; width: 580px; margin: 0 auto;" width="580" valign="top">
              <div class="content" style="box-sizing: border-box; display: block; margin: 0 auto; max-width: 580px; padding: 10px;">
                <!-- START CENTERED WHITE CONTAINER -->
                <table role="presentation" class="main" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; border-radius: 3px; width: 100%;" width="100%">
                  <!-- START MAIN CONTENT AREA -->
                  <tr>
                    <td class="wrapper" style="font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 20px;" valign="top">
                      <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                        <tr>
                          <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">
                            <div style="text-align: center; margin-bottom: 20px;">
                              <img src="https://via.placeholder.com/150x50?text=ProBASE" alt="ProBASE Logo" style="max-width: 150px;">
                            </div>
                            <h2 style="color: #333333; font-family: sans-serif; font-weight: 600; line-height: 1.4; margin: 0; margin-bottom: 20px;">Reset Your Password</h2>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">Hello,</p>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">You recently requested to reset your password for your #{@app_name} account. Click the button below to reset it.</p>
                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; box-sizing: border-box; width: 100%;" width="100%">
                              <tbody>
                                <tr>
                                  <td align="center" style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 15px;" valign="top">
                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;">
                                      <tbody>
                                        <tr>
                                          <td style="font-family: sans-serif; font-size: 14px; vertical-align: top; border-radius: 5px; text-align: center; background-color: #3498db;" valign="top" align="center" bgcolor="#3498db">
                                            <a href="#{reset_url}" target="_blank" style="border: solid 1px #3498db; border-radius: 5px; box-sizing: border-box; cursor: pointer; display: inline-block; font-size: 14px; font-weight: bold; margin: 0; padding: 12px 25px; text-decoration: none; text-transform: capitalize; background-color: #3498db; border-color: #3498db; color: #ffffff;">Reset Password</a>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">If you did not request a password reset, please ignore this email or contact support if you have questions.</p>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">This password reset link is only valid for the next 24 hours.</p>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">If you're having trouble clicking the password reset button, copy and paste the URL below into your web browser:</p>
                            <p style="font-family: sans-serif; font-size: 12px; font-weight: normal; margin: 0; margin-bottom: 15px; word-break: break-all;"><a href="#{reset_url}" style="color: #3498db; text-decoration: underline;">#{reset_url}</a></p>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">Thank you,<br>The #{@app_name} Team</p>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <!-- END MAIN CONTENT AREA -->
                </table>
                <!-- START FOOTER -->
                <div class="footer" style="clear: both; margin-top: 10px; text-align: center; width: 100%;">
                  <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                    <tr>
                      <td class="content-block" style="font-family: sans-serif; vertical-align: top; padding-bottom: 10px; padding-top: 10px; color: #999999; font-size: 12px; text-align: center;" valign="top" align="center">
                        <span class="apple-link" style="color: #999999; font-size: 12px; text-align: center;">#{@app_name}</span>
                      </td>
                    </tr>
                  </table>
                </div>
                <!-- END FOOTER -->
              </div>
            </td>
            <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">&nbsp;</td>
          </tr>
        </table>
      </body>
    </html>
    """
  end

  defp password_reset_text_body(_user, reset_url) do
    """
    RESET YOUR PASSWORD - #{@app_name}

    Hello,

    You recently requested to reset your password for your #{@app_name} account.
    Please visit the following link to reset your password:

    #{reset_url}

    This password reset link is only valid for the next 24 hours.

    If you did not request a password reset, please ignore this email or contact support if you have questions.

    Thank you,
    The #{@app_name} Team
    """
  end

  defp confirmation_html_body(_user, confirmation_url) do
    """
    <html>
      <body>
        <h2>Confirm your account</h2>
        <p>Welcome! Please confirm your account by clicking the link below:</p>
        <p><a href="#{confirmation_url}">Confirm Account</a></p>
        <p>If you didn't create this account, please ignore this email.</p>
      </body>
    </html>
    """
  end

  defp confirmation_text_body(_user, confirmation_url) do
    """
    Confirm your account

    Welcome! Please confirm your account by visiting the link below:

    #{confirmation_url}

    If you didn't create this account, please ignore this email.
    """
  end

  defp email_update_html_body(_user, old_email, update_url) do
    """
    <html>
      <body>
        <h2>Confirm your email change</h2>
        <p>You have requested to change your email from #{old_email}.</p>
        <p>Click the link below to confirm this change:</p>
        <p><a href="#{update_url}">Confirm Email Change</a></p>
        <p>If you didn't request this change, please ignore this email.</p>
      </body>
    </html>
    """
  end

  defp email_update_text_body(_user, old_email, update_url) do
    """
    Confirm your email change

    You have requested to change your email from #{old_email}.
    Visit the link below to confirm this change:

    #{update_url}

    If you didn't request this change, please ignore this email.
    """
  end

  defp welcome_html_body(user, temporary_password, login_url) do
    """
    <!DOCTYPE html>
    <html>
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
        <title>Welcome to #{@app_name}</title>
        <style>
          @media only screen and (max-width: 620px) {
            table.body h1 {
              font-size: 28px !important;
              margin-bottom: 10px !important;
            }
            table.body p,
            table.body ul,
            table.body ol,
            table.body td,
            table.body span,
            table.body a {
              font-size: 16px !important;
            }
            table.body .wrapper,
            table.body .article {
              padding: 10px !important;
            }
            table.body .content {
              padding: 0 !important;
            }
            table.body .container {
              padding: 0 !important;
              width: 100% !important;
            }
            table.body .main {
              border-left-width: 0 !important;
              border-radius: 0 !important;
              border-right-width: 0 !important;
            }
            table.body .btn table {
              width: 100% !important;
            }
            table.body .btn a {
              width: 100% !important;
            }
            table.body .img-responsive {
              height: auto !important;
              max-width: 100% !important;
              width: auto !important;
            }
          }
        </style>
      </head>
      <body style="background-color: #f6f6f6; font-family: sans-serif; -webkit-font-smoothing: antialiased; font-size: 14px; line-height: 1.4; margin: 0; padding: 0; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%;">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="body" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #f6f6f6; width: 100%;" width="100%" bgcolor="#f6f6f6">
          <tr>
            <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">&nbsp;</td>
            <td class="container" style="font-family: sans-serif; font-size: 14px; vertical-align: top; display: block; max-width: 580px; padding: 10px; width: 580px; margin: 0 auto;" width="580" valign="top">
              <div class="content" style="box-sizing: border-box; display: block; margin: 0 auto; max-width: 580px; padding: 10px;">
                <!-- START CENTERED WHITE CONTAINER -->
                <table role="presentation" class="main" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; background: #ffffff; border-radius: 3px; width: 100%;" width="100%">
                  <!-- START MAIN CONTENT AREA -->
                  <tr>
                    <td class="wrapper" style="font-family: sans-serif; font-size: 14px; vertical-align: top; box-sizing: border-box; padding: 20px;" valign="top">
                      <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                        <tr>
                          <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">
                            <div style="text-align: center; margin-bottom: 20px;">
                              <img src="https://via.placeholder.com/150x50?text=ProBASE" alt="ProBASE Logo" style="max-width: 150px;">
                            </div>
                            <h2 style="color: #333333; font-family: sans-serif; font-weight: 600; line-height: 1.4; margin: 0; margin-bottom: 20px;">Welcome to #{@app_name}!</h2>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">Hello,</p>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">Your account has been created successfully! You can now access #{@app_name} using the credentials below.</p>

                            <div style="background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 5px; padding: 20px; margin: 20px 0;">
                              <h3 style="color: #333333; font-family: sans-serif; font-weight: 600; line-height: 1.4; margin: 0; margin-bottom: 15px;">Your Login Credentials</h3>
                              <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 10px;"><strong>Email:</strong> #{user.email}</p>
                              <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;"><strong>Temporary Password:</strong> <code style="background-color: #e9ecef; padding: 2px 4px; border-radius: 3px; font-family: monospace;">#{temporary_password}</code></p>
                              <p style="font-family: sans-serif; font-size: 12px; font-weight: normal; margin: 0; color: #6c757d;"><em>⚠️ For security reasons, please change your password after your first login.</em></p>
                            </div>

                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" class="btn btn-primary" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; box-sizing: border-box; width: 100%;" width="100%">
                              <tbody>
                                <tr>
                                  <td align="center" style="font-family: sans-serif; font-size: 14px; vertical-align: top; padding-bottom: 15px;" valign="top">
                                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: auto;">
                                      <tbody>
                                        <tr>
                                          <td style="font-family: sans-serif; font-size: 14px; vertical-align: top; border-radius: 5px; text-align: center; background-color: #28a745;" valign="top" align="center" bgcolor="#28a745">
                                            <a href="#{login_url}" target="_blank" style="border: solid 1px #28a745; border-radius: 5px; box-sizing: border-box; cursor: pointer; display: inline-block; font-size: 14px; font-weight: bold; margin: 0; padding: 12px 25px; text-decoration: none; text-transform: capitalize; background-color: #28a745; border-color: #28a745; color: #ffffff;">Login to Your Account</a>
                                          </td>
                                        </tr>
                                      </tbody>
                                    </table>
                                  </td>
                                </tr>
                              </tbody>
                            </table>

                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">Welcome aboard!</p>
                            <p style="font-family: sans-serif; font-size: 14px; font-weight: normal; margin: 0; margin-bottom: 15px;">Best regards,<br>The #{@app_name} Team</p>
                          </td>
                        </tr>
                      </table>
                    </td>
                  </tr>
                  <!-- END MAIN CONTENT AREA -->
                </table>
                <!-- START FOOTER -->
                <div class="footer" style="clear: both; margin-top: 10px; text-align: center; width: 100%;">
                  <table role="presentation" border="0" cellpadding="0" cellspacing="0" style="border-collapse: separate; mso-table-lspace: 0pt; mso-table-rspace: 0pt; width: 100%;" width="100%">
                    <tr>
                      <td class="content-block" style="font-family: sans-serif; vertical-align: top; padding-bottom: 10px; padding-top: 10px; color: #999999; font-size: 12px; text-align: center;" valign="top" align="center">
                        <span class="apple-link" style="color: #999999; font-size: 12px; text-align: center;">#{@app_name}</span>
                      </td>
                    </tr>
                  </table>
                </div>
                <!-- END FOOTER -->
              </div>
            </td>
            <td style="font-family: sans-serif; font-size: 14px; vertical-align: top;" valign="top">&nbsp;</td>
          </tr>
        </table>
      </body>
    </html>
    """
  end

  defp welcome_text_body(user, temporary_password, login_url) do
    """
    WELCOME TO #{@app_name}!

    Hello,

    Your account has been created successfully! You can now access #{@app_name} using the credentials below.

    YOUR LOGIN CREDENTIALS:
    Email: #{user.email}
    Temporary Password: #{temporary_password}

    ⚠️ For security reasons, please change your password after your first login.

    Login here: #{login_url}

    If you have any questions or need assistance, please don't hesitate to contact our support team.

    Welcome aboard!

    Best regards,
    The #{@app_name} Team
    """
  end

  @doc """
  Delivers an email using the configured mailer.
  """
  def deliver(email) do
    Mailer.deliver(email)
  end
end
