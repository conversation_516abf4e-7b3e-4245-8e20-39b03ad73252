defmodule Reconciliation.Services.FileProcessingService do
  @moduledoc """
  Service for handling file upload and processing operations.
  Extracted from LiveView modules to follow proper separation of concerns.
  """

  require Logger
  alias Reconciliation.{UploadedFile, Repo}
  alias Reconciliation.Services.ExcelParser

  @doc """
  Process uploaded file with progress tracking
  """
  def process_file_with_progress(uploaded_file, reconciliation_run_id, progress_callback \\ nil) do
    try do
      # Validate file still exists
      unless File.exists?(uploaded_file.file_path) do
        raise "File no longer exists: #{uploaded_file.file_path}"
      end

      # Broadcast start of database insertion
      broadcast_progress(reconciliation_run_id, uploaded_file.id, %{
        status: "inserting",
        progress: 0,
        message: "Starting database insertion..."
      })

      # Update file status to processing
      case Reconciliation.update_uploaded_file(uploaded_file, %{status: "processing"}) do
        {:ok, _} -> :ok
        {:error, changeset} ->
          error_msg = format_changeset_errors(changeset)
          raise "Failed to update file status: #{error_msg}"
      end

      # Parse file with progress tracking
      case ExcelParser.parse_file_with_progress(uploaded_file, fn progress ->
        broadcast_progress(reconciliation_run_id, uploaded_file.id, progress)
        if progress_callback, do: progress_callback.(progress)
      end) do
        {:ok, result} ->
          # Broadcast completion
          broadcast_completion(reconciliation_run_id, uploaded_file.id, result)
          {:ok, result}

        {:error, error} ->
          # Mark file as failed and broadcast error
          Reconciliation.mark_file_failed(uploaded_file, [error])
          broadcast_error(reconciliation_run_id, uploaded_file.id, format_user_friendly_error(error))
          {:error, error}
      end

    rescue
      error ->
        # Mark file as failed
        Reconciliation.mark_file_failed(uploaded_file, [Exception.message(error)])
        broadcast_error(reconciliation_run_id, uploaded_file.id, format_user_friendly_error(Exception.message(error)))
        {:error, Exception.message(error)}
    end
  end

  @doc """
  Handle file upload and create database record
  """
  def handle_file_upload(%{path: temp_path} = _meta, entry, file_type, reconciliation_run_id) do
    try do
      # Define a permanent storage directory
      uploads_dir_base = Application.app_dir(:reconciliation, "priv/repo/uploads")
      run_specific_uploads_dir = Path.join(uploads_dir_base, "reconciliation_runs/#{reconciliation_run_id}")
      File.mkdir_p!(run_specific_uploads_dir)

      # Generate a unique filename for storage to avoid collisions
      unique_filename = generate_filename(entry.client_name)
      destination_path = Path.join(run_specific_uploads_dir, unique_filename)

      # Validate file exists at temporary path and is readable
      case File.stat(temp_path) do
        {:ok, %{size: size}} when size > 0 ->
          # Copy the file from the temporary path to the permanent destination
          File.cp!(temp_path, destination_path)
          Logger.info("[#{reconciliation_run_id}] Copied uploaded file from #{temp_path} to #{destination_path}")

          # Create uploaded file record with the new permanent path
          file_attrs = %{
            reconciliation_run_id: reconciliation_run_id,
            file_type: file_type,
            filename: unique_filename,
            original_filename: entry.client_name,
            file_size: size,
            mime_type: entry.client_type,
            file_path: destination_path,
            status: "uploaded"
          }

          case Reconciliation.create_uploaded_file(file_attrs) do
            {:ok, uploaded_file} ->
              {:ok, uploaded_file}
            {:error, changeset} ->
              error_msg = format_changeset_errors(changeset)
              {:error, "Failed to save file information: #{error_msg}"}
          end

        {:ok, %{size: 0}} ->
          {:error, "File is empty"}

        {:error, reason} ->
          {:error, "Cannot read file: #{inspect(reason)}"}
      end
    rescue
      error ->
        {:error, "Upload failed: #{Exception.message(error)}"}
    end
  end

  @doc """
  Generate unique filename to avoid collisions
  """
  def generate_filename(original_name) do
    timestamp = DateTime.utc_now() |> DateTime.to_unix()
    extension = Path.extname(original_name)
    base_name = Path.basename(original_name, extension)
    "#{base_name}_#{timestamp}#{extension}"
  end

  # Private helper functions

  defp broadcast_progress(reconciliation_run_id, file_id, progress) do
    Phoenix.PubSub.broadcast(
      Reconciliation.PubSub,
      "upload_progress:#{reconciliation_run_id}",
      {:database_progress, file_id, progress}
    )
  end

  defp broadcast_completion(reconciliation_run_id, file_id, result) do
    Phoenix.PubSub.broadcast(
      Reconciliation.PubSub,
      "upload_progress:#{reconciliation_run_id}",
      {:database_complete, file_id, result}
    )
  end

  defp broadcast_error(reconciliation_run_id, file_id, error) do
    Phoenix.PubSub.broadcast(
      Reconciliation.PubSub,
      "upload_progress:#{reconciliation_run_id}",
      {:database_error, file_id, error}
    )
  end

  defp format_user_friendly_error(error) when is_binary(error) do
    cond do
      String.contains?(error, "Unsupported file format") ->
        "This file format is not supported. Please upload an Excel (.xlsx, .xls) or CSV file."

      String.contains?(error, "Failed to parse") ->
        "Unable to read the file. Please check that the file is not corrupted and try again."

      String.contains?(error, "File is empty") ->
        "The uploaded file appears to be empty. Please check your file and try again."

      String.contains?(error, "Missing or invalid amount") ->
        "Some rows are missing required amount values. Please check your data and try again."

      String.contains?(error, "no data") ->
        "No transaction data found in the file. Please check that your file contains the expected data."

      true ->
        "An error occurred while processing your file. Please try again or contact support if the problem persists."
    end
  end
  defp format_user_friendly_error(error), do: "An unexpected error occurred: #{inspect(error)}"

  defp format_changeset_errors(changeset) do
    changeset.errors
    |> Enum.map(fn {field, {message, _}} -> "#{field}: #{message}" end)
    |> Enum.join(", ")
  end
end
