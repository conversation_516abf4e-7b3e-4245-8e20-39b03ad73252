import Config

# Configure your database
config :reconciliation, Reconciliation.Repo,
  username: "postg<PERSON>",
  password: "1234",
  hostname: "localhost",
  database: "reconcilliation",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

# For development, we disable any cache and enable
# debugging and code reloading.
#
# The watchers configuration can be used to run external
# watchers to your application. For example, we can use it
# to bundle .js and .css sources.
#
# Configure Swoosh email preview server
config :swoosh, :preview_server, true
config :swoosh, serve_mailbox: true
config :swoosh, preview_port: 4001
config :reconciliation, ReconciliationWeb.Endpoint,
  # Binding to loopback ipv4 address prevents access from other machines.
  # Change to `ip: {0, 0, 0, 0}` to allow access from other machines.
  http: [ip: {127, 0, 0, 1}, port: 4000],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "1aDhoDv1BTNd2zSXyTNHbAJxghl79owwU6S4RKMdSe3vSXRCUZAx9lDvf2mhZiAb",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:reconciliation, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:reconciliation, ~w(--watch)]}
  ]

# ## SSL Support
#
# In order to use HTTPS in development, a self-signed
# certificate can be generated by running the following
# Mix task:
#
#     mix phx.gen.cert
#
# Run `mix help phx.gen.cert` for more information.
#
# The `http:` config above can be replaced with:
#
#     https: [
#       port: 4001,
#       cipher_suite: :strong,
#       keyfile: "priv/cert/selfsigned_key.pem",
#       certfile: "priv/cert/selfsigned.pem"
#     ],
#
# If desired, both `http:` and `https:` keys can be
# configured to run both http and https servers on
# different ports.

# Watch static and templates for browser reloading.
config :reconciliation, ReconciliationWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/(?!uploads/).*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/reconciliation_web/(controllers|live|components)/.*(ex|heex)$"
    ]
  ]

# Enable dev routes for dashboard and mailbox
config :reconciliation, dev_routes: true

# Do not include metadata nor timestamps in development logs
config :logger, :console, format: "[$level] $message\n"

# Set a higher stacktrace during development. Avoid configuring such
# in production as building large stacktraces may be expensive.
config :phoenix, :stacktrace_depth, 20

# Initialize plugs at runtime for faster development compilation
config :phoenix, :plug_init_mode, :runtime

config :phoenix_live_view,
  # Include HEEx debug annotations as HTML comments in rendered markup
  debug_heex_annotations: true,
  # Enable helpful, but potentially expensive runtime checks
  enable_expensive_runtime_checks: true

# Load .env file if it exists
if File.exists?(".env") do
  File.read!(".env")
  |> String.split("\n")
  |> Enum.filter(&(String.trim(&1) != "" && !String.starts_with?(String.trim(&1), "#")))
  |> Enum.each(fn line ->
    case String.split(line, "=", parts: 2) do
      [key, value] -> System.put_env(String.trim(key), String.trim(value))
      _ -> :ok
    end
  end)
end

# Configure Swoosh mailer for development
# Use Resend in development
config :reconciliation, Reconciliation.Mailer,
  adapter: Resend.Swoosh.Adapter,
  api_key: System.get_env("RESEND_API_KEY")

# Use Local adapter for development (uncomment to test without sending real emails)
# config :reconciliation, Reconciliation.Mailer,
#   adapter: Swoosh.Adapters.Local

# Enable Swoosh mailbox preview in development
config :swoosh, :api_client, false


