defmodule Reconciliation.UploadedFileTest do
  use ExUnit.Case

  alias Reconciliation.UploadedFile

  describe "display_name/1" do
    test "returns original filename when available" do
      file = %UploadedFile{original_filename: "bank_statement.xlsx", filename: "bank_statement_123.xlsx"}
      assert UploadedFile.display_name(file) == "bank_statement.xlsx"
    end

    test "returns filename when original_filename is nil" do
      file = %UploadedFile{original_filename: nil, filename: "bank_statement_123.xlsx"}
      assert UploadedFile.display_name(file) == "bank_statement_123.xlsx"
    end

    test "returns 'Unknown File' when both are nil" do
      file = %UploadedFile{original_filename: nil, filename: nil}
      assert UploadedFile.display_name(file) == "Unknown File"
    end

    test "sanitizes HTML characters" do
      file = %UploadedFile{original_filename: "file<script>alert('xss')</script>.xlsx"}
      assert UploadedFile.display_name(file) == "filescriptalert('xss')/script.xlsx"
    end
  end

  describe "short_display_name/2" do
    test "truncates long filenames" do
      file = %UploadedFile{original_filename: "very_long_bank_statement_filename_that_exceeds_limit.xlsx"}
      result = UploadedFile.short_display_name(file, 20)
      assert String.length(result) <= 20
      assert String.ends_with?(result, "...")
    end

    test "keeps short filenames unchanged" do
      file = %UploadedFile{original_filename: "short.xlsx"}
      assert UploadedFile.short_display_name(file, 20) == "short.xlsx"
    end
  end

  describe "short_name/1" do
    test "creates short identifier from original filename" do
      file = %UploadedFile{original_filename: "bank_statement_january.xlsx"}
      assert UploadedFile.short_name(file) == "bank_sta.xlsx"
    end

    test "handles files without extension" do
      file = %UploadedFile{original_filename: "bank_statement"}
      assert UploadedFile.short_name(file) == "bank_sta"
    end
  end


end
