<div class="max-w-4xl mx-auto p-6">
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 mb-2">Financial Reconciliation</h1>
    <p class="text-gray-600 mb-3">Upload two financial documents to compare and reconcile transactions automatically</p>
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
      <p class="text-sm text-gray-700">
        <strong>How it works:</strong> Upload your primary document (like a bank statement) and a comparison document (like receipts or invoices).
        Our system will automatically match transactions between the two files and highlight any discrepancies for your review.
      </p>
    </div>
  </div>

  <!-- Reconciliation Run Name -->
  <div class="mb-8 bg-white rounded-lg shadow-sm border p-6">
    <h2 class="text-lg font-semibold text-gray-900 mb-4">Reconciliation Details</h2>
    <.form for={@form} phx-change="update_name" class="space-y-4">
      <div>
        <.label for="name">Reconciliation Name</.label>
        <.input
          field={@form[:name]}
          type="text"
          placeholder="Enter a name for this reconciliation run"
          class="mt-1"
        />
      </div>
    </.form>
  </div>

  <!-- File Upload Section -->
  <.form for={@form} phx-submit="save" phx-change="validate" class="space-y-8">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

      <!-- Primary Document Upload -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
            <span class="text-blue-600 font-semibold">1</span>
          </div>
          <div class="flex-1">
            <%= if @file_a_uploaded do %>
              <h3 class="text-lg font-semibold text-gray-900"><%= Reconciliation.UploadedFile.short_display_name(@file_a_uploaded, 35) %></h3>
              <p class="text-sm text-gray-500">Primary Document</p>
            <% else %>
              <h3 class="text-lg font-semibold text-gray-900">Primary Document</h3>
            <% end %>
          </div>
        </div>

        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">
            Upload your primary reference document - this will be the main source for comparison.
          </p>
          <div class="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p class="text-xs text-blue-700 font-medium mb-1">Common examples:</p>
            <ul class="text-xs text-blue-600 space-y-1">
              <li>• Bank statements or account summaries</li>
              <li>• General ledger or main accounting records</li>
              <li>• Invoice registers or billing summaries</li>
              <li>• Credit card statements</li>
            </ul>
          </div>
        </div>

        <%= if @file_a_uploaded do %>
          <div class="border-2 border-green-300 bg-green-50 rounded-lg p-6 text-center">
            <div class="text-green-600">
              <.icon name="hero-check-circle" class="w-12 h-12 mx-auto mb-2" />
              <p class="font-medium"><%= @file_a_uploaded.original_filename %></p>
              <p class="text-sm text-gray-500">File uploaded successfully</p>
            </div>
          </div>
        <% else %>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <div class="text-center mb-4">
              <.icon name="hero-document-arrow-up" class="w-12 h-12 mx-auto mb-2 text-gray-400" />
              <p class="font-medium text-gray-700">Select Primary Document</p>
              <p class="text-sm text-gray-500">Excel (.xlsx, .xls) or CSV files up to 50MB</p>
            </div>

            <div class="flex justify-center">
              <.live_file_input upload={@uploads.file_a} class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 cursor-pointer" />
            </div>
          </div>
        <% end %>



        <!-- File A Upload Progress -->
        <div :for={entry <- @uploads.file_a.entries} class="mt-4">
          <.upload_progress_card
            entry={entry}
            file_type="file_a"
            upload_status={Map.get(@upload_status, entry.ref, %{})}
            database_status={get_database_status_for_entry(@database_status, @file_a_uploaded)}
          />
        </div>

        <!-- File A Errors -->
        <div :for={{_ref, err} <- @uploads.file_a.errors} class="mt-2 text-red-600 text-sm">
          <.icon name="hero-exclamation-triangle" class="w-4 h-4 inline mr-1" />
          <%= error_to_string(err) %>
        </div>
      </div>

      <!-- Comparison Document Upload -->
      <div class="bg-white rounded-lg shadow-sm border p-6">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
            <span class="text-green-600 font-semibold">2</span>
          </div>
          <div class="flex-1">
            <%= if @file_b_uploaded do %>
              <h3 class="text-lg font-semibold text-gray-900"><%= Reconciliation.UploadedFile.short_display_name(@file_b_uploaded, 35) %></h3>
              <p class="text-sm text-gray-500">Comparison Document</p>
            <% else %>
              <h3 class="text-lg font-semibold text-gray-900">Comparison Document</h3>
            <% end %>
          </div>
        </div>

        <div class="mb-4">
          <p class="text-sm text-gray-600 mb-2">
            Upload your comparison document - this will be compared against the primary document.
          </p>
          <div class="bg-green-50 border border-green-200 rounded-lg p-3">
            <p class="text-xs text-green-700 font-medium mb-1">Common examples:</p>
            <ul class="text-xs text-green-600 space-y-1">
              <li>• Receipt collections or expense reports</li>
              <li>• Secondary ledger or subsidiary records</li>
              <li>• Vendor statements or supplier invoices</li>
              <li>• Transaction exports from other systems</li>
            </ul>
          </div>
        </div>

        <%= if @file_b_uploaded do %>
          <div class="border-2 border-green-300 bg-green-50 rounded-lg p-6 text-center">
            <div class="text-green-600">
              <.icon name="hero-check-circle" class="w-12 h-12 mx-auto mb-2" />
              <p class="font-medium"><%= @file_b_uploaded.original_filename %></p>
              <p class="text-sm text-gray-500">File uploaded successfully</p>
            </div>
          </div>
        <% else %>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6">
            <div class="text-center mb-4">
              <.icon name="hero-document-arrow-up" class="w-12 h-12 mx-auto mb-2 text-gray-400" />
              <p class="font-medium text-gray-700">Select Comparison Document</p>
              <p class="text-sm text-gray-500">Excel (.xlsx, .xls) or CSV files up to 50MB</p>
            </div>

            <div class="flex justify-center">
              <.live_file_input upload={@uploads.file_b} class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-green-50 file:text-green-700 hover:file:bg-green-100 cursor-pointer" />
            </div>
          </div>
        <% end %>



        <!-- File B Upload Progress -->
        <div :for={entry <- @uploads.file_b.entries} class="mt-4">
          <.upload_progress_card
            entry={entry}
            file_type="file_b"
            upload_status={Map.get(@upload_status, entry.ref, %{})}
            database_status={get_database_status_for_entry(@database_status, @file_b_uploaded)}
          />
        </div>

        <!-- File B Errors -->
        <div :for={{_ref, err} <- @uploads.file_b.errors} class="mt-2 text-red-600 text-sm">
          <.icon name="hero-exclamation-triangle" class="w-4 h-4 inline mr-1" />
          <%= error_to_string(err) %>
        </div>
      </div>
    </div>

    <!-- Submit Button -->
    <div class="text-center">
      <%= if @file_a_client_upload_complete && @file_b_client_upload_complete do %>
        <.button
          type="submit"
          phx-disable-with="Processing..."
          class="bg-grey-400 hover:bg-grey-500 text-white px-8 py-3 text-lg font-medium"
          style="background-color: #4ade80; hover:background-color: #22c55e;"
        >
          <.icon name="hero-play" class="w-5 h-5 mr-2" />
          upload
        </.button>
      <% else %>
        <p class="text-gray-500 text-sm">
          Please upload both files to continue
        </p>
      <% end %>
    </div>
  </.form>

  <!-- Confirmation Dialog -->
  <%= if @show_confirmation do %>
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" phx-click="cancel_confirmation">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white" phx-click="stop_propagation">
        <div class="mt-3">
          <div class="flex items-center justify-center mb-4">
            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
              <.icon name="hero-exclamation-triangle" class="w-6 h-6 text-yellow-600" />
            </div>
          </div>

          <div class="text-center">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Confirm File Upload</h3>
            <p class="text-sm text-gray-600 mb-6">
              Are you sure you want to upload and process these files? This will start the upload process and redirect you to the transactions page.
            </p>

            <div class="flex justify-center space-x-4">
              <button
                phx-click="cancel_confirmation"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
              >
                Cancel
              </button>
              <button
                phx-click="confirm_upload"
                class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                Yes, Upload 
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  <% end %>

  <!-- Instructions -->
  <div class="mt-12 bg-gray-50 rounded-lg p-6">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">How it works</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="text-center">
        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <.icon name="hero-document-arrow-up" class="w-6 h-6 text-blue-600" />
        </div>
        <h4 class="font-medium text-gray-900 mb-2">1. Upload Files</h4>
        <p class="text-sm text-gray-600">Upload two Excel or CSV files containing transaction data</p>
      </div>
      <div class="text-center">
        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <.icon name="hero-magnifying-glass" class="w-6 h-6 text-green-600" />
        </div>
        <h4 class="font-medium text-gray-900 mb-2">2. Auto-Match</h4>
        <p class="text-sm text-gray-600">Our system automatically finds matching transactions</p>
      </div>
      <div class="text-center">
        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
          <.icon name="hero-chart-bar" class="w-6 h-6 text-purple-600" />
        </div>
        <h4 class="font-medium text-gray-900 mb-2">3. Review Results</h4>
        <p class="text-sm text-gray-600">Get detailed reconciliation reports and insights</p>
      </div>
    </div>
  </div>
</div>