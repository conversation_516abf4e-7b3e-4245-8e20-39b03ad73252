defmodule Reconciliation.FileTypeDetector do
  @moduledoc """
  Intelligent file type detection based on filename patterns.
  Provides human-readable labels for common financial document types.
  """

  @doc """
  Detects the likely document type based on filename patterns.
  Returns a human-readable label for the document type.
  """
  def detect_file_type(filename) when is_binary(filename) do
    filename_lower = String.downcase(filename)
    
    cond do
      # Bank statements
      contains_any?(filename_lower, ["bank", "statement", "account", "checking", "savings"]) ->
        "Bank Statement"
      
      # Invoices
      contains_any?(filename_lower, ["invoice", "bill", "billing"]) ->
        "Invoice"
      
      # Receipts
      contains_any?(filename_lower, ["receipt", "receipts"]) ->
        "Receipt"
      
      # Expenses
      contains_any?(filename_lower, ["expense", "expenses", "expens"]) ->
        "Expense Report"
      
      # Payroll
      contains_any?(filename_lower, ["payroll", "salary", "wages", "pay"]) ->
        "Payroll"
      
      # Ledger
      contains_any?(filename_lower, ["ledger", "journal", "gl", "general_ledger"]) ->
        "Ledger"
      
      # Credit card
      contains_any?(filename_lower, ["credit", "card", "cc", "visa", "mastercard", "amex"]) ->
        "Credit Card Statement"
      
      # Transactions
      contains_any?(filename_lower, ["transaction", "transactions", "txn", "trans"]) ->
        "Transaction Report"
      
      # Cash flow
      contains_any?(filename_lower, ["cash", "flow", "cashflow"]) ->
        "Cash Flow"
      
      # Reconciliation
      contains_any?(filename_lower, ["reconcil", "recon"]) ->
        "Reconciliation"
      
      # Purchase orders
      contains_any?(filename_lower, ["purchase", "po", "order"]) ->
        "Purchase Order"
      
      # Vendor/supplier
      contains_any?(filename_lower, ["vendor", "supplier", "ap", "payable"]) ->
        "Vendor Statement"
      
      # Customer/receivables
      contains_any?(filename_lower, ["customer", "ar", "receivable", "aging"]) ->
        "Customer Statement"
      
      # Trial balance
      contains_any?(filename_lower, ["trial", "balance", "tb"]) ->
        "Trial Balance"
      
      # Budget
      contains_any?(filename_lower, ["budget", "forecast"]) ->
        "Budget"
      
      # Tax related
      contains_any?(filename_lower, ["tax", "1099", "w2", "irs"]) ->
        "Tax Document"
      
      # Default fallback
      true ->
        nil
    end
  end
  
  def detect_file_type(_), do: nil

  @doc """
  Gets a smart display name for a file, combining the detected type with the cleaned filename.
  """
  def smart_display_name(filename) when is_binary(filename) do
    detected_type = detect_file_type(filename)
    clean_name = clean_filename(filename)
    
    case detected_type do
      nil -> clean_name
      type when clean_name == type -> type
      type -> "#{type} - #{clean_name}"
    end
  end
  
  def smart_display_name(_), do: "Document"

  @doc """
  Gets a short smart display name for UI components with limited space.
  """
  def smart_short_name(filename, max_length \\ 25) when is_binary(filename) do
    detected_type = detect_file_type(filename)
    clean_name = clean_filename(filename)
    
    result = case detected_type do
      nil -> clean_name
      type when clean_name == type -> type
      type when String.length(type) <= max_length -> type
      type -> "#{type} - #{clean_name}"
    end
    
    if String.length(result) > max_length do
      String.slice(result, 0, max_length - 3) <> "..."
    else
      result
    end
  end
  
  def smart_short_name(_, _), do: "Document"

  @doc """
  Provides contextual help text based on detected file type.
  """
  def get_help_text(filename) when is_binary(filename) do
    case detect_file_type(filename) do
      "Bank Statement" -> 
        "Bank statements typically contain transaction details, dates, amounts, and account balances."
      
      "Invoice" -> 
        "Invoices contain billing information, line items, amounts, and payment terms."
      
      "Receipt" -> 
        "Receipts show proof of payment for goods or services with transaction details."
      
      "Expense Report" -> 
        "Expense reports detail business expenses with categories, amounts, and dates."
      
      "Credit Card Statement" -> 
        "Credit card statements show charges, payments, and account activity."
      
      "Transaction Report" -> 
        "Transaction reports contain detailed records of financial transactions."
      
      _ -> 
        "Financial documents with transaction data, amounts, dates, and descriptions."
    end
  end
  
  def get_help_text(_), do: "Financial documents with transaction data."

  # Private helper functions
  
  defp contains_any?(text, patterns) do
    Enum.any?(patterns, &String.contains?(text, &1))
  end
  
  defp clean_filename(filename) do
    filename
    |> Path.basename(Path.extname(filename))  # Remove extension
    |> String.replace(~r/_+/, " ")            # Replace underscores with spaces
    |> String.replace(~r/-+/, " ")            # Replace dashes with spaces
    |> String.replace(~r/\s+/, " ")           # Normalize multiple spaces
    |> String.trim()                          # Remove leading/trailing spaces
    |> String.split(" ")                      # Split into words
    |> Enum.map(&String.capitalize/1)         # Capitalize each word
    |> Enum.join(" ")                         # Join back together
    |> case do
      "" -> "Document"                        # Fallback for empty names
      name when byte_size(name) > 40 ->
        String.slice(name, 0, 40) <> "..."   # Truncate very long names
      name -> name
    end
  end
end
